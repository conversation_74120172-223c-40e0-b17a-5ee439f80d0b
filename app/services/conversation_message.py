from datetime import date
import logging
from typing import List, Sequence, cast
from uuid import UUID

from fastapi import UploadFile

from config import settings
from constants.extracted_data import ConfirmedDataFields, ConversationState
from constants.message import (
    ALL_REQUIRED_FIELDS_EXTRACTED_DOCS,
    ConversationMessageIntention,
    MessageRole,
    MessageType,
    OptionType,
    SuggestedUserPrompt,
    SystemReplyType,
)
from exceptions import EntityNotFoundError
from repositories import ConversationMessageRepository, ConversationRepository, DocumentDbRepository
from schemas import (
    AggregatedData,
    BaseMessageSerializer,
    ClientNameOption,
    CombinedMessageSerializer,
    ConfirmedData,
    ConversationMessageProcessingResult,
    DatePickerOption,
    DocumentCreationRequest,
    LDMFCountryOption,
    MessageValidator,
    Option,
    SystemMessageSerializer,
    UserMessageSerializer,
)
from services.date_validator import DateValidatorService
from services.document import DocumentService
from services.extracted_data import ExtractedDataService
from services.intent_classifier import IntentClassifierService
from services.kx_dash import KXDashService
from services.message_processor import ConversationMessageProcessor
from services.proactive_chat import ProactiveChatService
from services.suggestions import SuggestedPromptsGenerator
from services.system_message_generation import SystemMessageGenerationService


__all__ = ['ConversationMessageService']

logger = logging.getLogger(__name__)


class ConversationMessageService:
    """Service for conversation message-related business logic."""

    def __init__(
        self,
        conversation_message_repository: ConversationMessageRepository,
        conversation_repository: ConversationRepository,
        document_service: DocumentService,
        document_db_repository: DocumentDbRepository,
        kx_dash_service: KXDashService,
        intent_classifier_service: IntentClassifierService,
        extracted_data_service: ExtractedDataService,
        date_validator_service: DateValidatorService,
        system_message_generation_service: SystemMessageGenerationService,
    ):
        self.conversation_message_repository = conversation_message_repository
        self.conversation_repository = conversation_repository
        self.document_service = document_service
        self.document_db_repository = document_db_repository
        self.kx_dash_service = kx_dash_service
        self.intent_classifier_service = intent_classifier_service
        self.extracted_data_service = extracted_data_service
        self.date_validator_service = date_validator_service
        self.system_message_service = system_message_generation_service

    async def create(
        self,
        conversation_id: UUID,
        content: str,
        selected_option: Option | None,
        files: list[UploadFile] | None,
        token: str,
    ) -> CombinedMessageSerializer:
        """
        Create a new conversation message with attached files.

        Args:
            conversation_id: UUID of the conversation
            content: Text content of the message
            selected_option: The selected option
            files: Optional list of files to attach

        Returns:
            Response containing both user message and system message with expected entity

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            MaximumDocumentsNumberExceeded: If too many documents are attached
            MaximumDocumentsSizeExceeded: If documents exceed size limit
            ValueError: If file validation fails
        """

        content = content.strip()
        if files:
            message_type = MessageType.TEXT_WITH_FILE if content else MessageType.FILE
        else:
            message_type = MessageType.TEXT

        user_message_to_persist = MessageValidator(
            conversation_id=conversation_id,
            role=MessageRole.USER,
            type=message_type,
            content=content,
            selected_option=selected_option,
            files=files,
            system_reply_type=None,
        )

        user_message = await self.create_message(user_message_to_persist)
        user_message = cast(UserMessageSerializer, user_message)

        aggregated_data = None
        intention = None
        confirmed_data = None

        if selected_option:
            match selected_option.type:
                case OptionType.CLIENT_NAME:
                    system_message = await self._handle_client_name_selection(selected_option, conversation_id)
                case OptionType.LDMF_COUNTRY:
                    system_message = await self._handle_country_selection(selected_option, conversation_id)
                case OptionType.DATES:
                    system_message = await self._handle_dates_selection(selected_option, conversation_id)
                case OptionType.KX_DASH_TASK:
                    system_message = await self.kx_dash_service.on_select(selected_option, conversation_id, token=token)
                case _:
                    raise NotImplementedError(f'Processing of {selected_option.type} option is not yet implemented')
        else:
            # Process the user message to get the system reply and intention
            message_processor = ConversationMessageProcessor(
                conversation_id=conversation_id,
                user_message=user_message,
                intent_classifier_service=self.intent_classifier_service,
                extracted_data_service=self.extracted_data_service,
                conversation_repository=self.conversation_repository,
                date_validator_service=self.date_validator_service,
                document_service=self.document_service,
                token=token,
            )
            message_processing_result: ConversationMessageProcessingResult = await message_processor.run()
            intention = message_processing_result.intention
            user_message_to_persist.intention = intention

            # Get aggregated extracted and confirmed data after message processing.
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

            # Get conversation message history for suggested prompts tracking.
            conversation_message_history = await self.conversation_message_repository.get_combined_history(
                conversation_id
            )

            suggested_prompts = await self.get_suggested_prompts(
                conversation_message_history=conversation_message_history,
                conversation_id=conversation_id,
                user_message=user_message,
                files=files,
                token=token,
                aggregated_data=aggregated_data,
                confirmed_data=confirmed_data,
                intention=intention,
            )

            # Update the user message with the determined intention
            await self.update_message_fields(user_message.id, {'Intention': intention})

            if intention == ConversationMessageIntention.DASH_DISCARD:
                dash_discard_message = await self._get_dash_discard_response(
                    user_message, message_processing_result, suggested_prompts
                )
                return dash_discard_message

            system_message = None
            should_generate_system_message = not (
                (content and intention == ConversationMessageIntention.EXTRACTION) or files
            )
            if should_generate_system_message:
                system_message = await self._get_system_message_with_result(
                    user_message_to_persist, message_processing_result, suggested_prompts
                )
                if not system_message.content:
                    system_message = None

        if not aggregated_data:
            aggregated_data = await self.extracted_data_service.aggregate_data(conversation_id)

        if not confirmed_data:
            confirmed_data = await self.conversation_repository.get_confirmed_data(conversation_id)

        # Append enriched message to system message
        if system_message:

            # conversation_message_history = await self.conversation_message_repository.get_combined_history(
            #     conversation_id
            # )

            # suggested_prompts = await self.get_suggested_prompts(
            #     conversation_message_history=conversation_message_history,
            #     conversation_id=conversation_id,
            #     user_message=user_message,
            #     files=files,
            #     token=token,
            #     aggregated_data=aggregated_data,
            #     confirmed_data=confirmed_data,
            #     intention=ConversationMessageIntention.EXTRACTION,
            # )
            proactive_chat_service = ProactiveChatService(
                aggregated_data=aggregated_data, confirmed_data=confirmed_data
            )
            # Get proactive message content
            enriched_message_content = proactive_chat_service.get_enriched_system_message()
            system_message.content = f'{system_message.content}\n\n{enriched_message_content}'

            # Get proactive options
            if enriched_message_content and not system_message.options:
                missing_data_response = await self.extracted_data_service.get_missing_required_data_prompts(
                    conversation_id=conversation_id,
                    token=token,
                    confirmed_data=confirmed_data,
                )
                proactive_options = self._convert_to_option_objects(
                    missing_data_response.options, missing_data_response.conversation_state
                )
                system_message.options = proactive_options

        # Prepare & return response
        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.create_message(system_message)) if system_message else None,
        )

        # Handle unified queue messaging for files and/or text content
        if files or content and intention == ConversationMessageIntention.EXTRACTION:
            document_data = DocumentCreationRequest(
                conversation_id=user_message.conversation_id,
                files=files or [],
                message_id=response.user.id,
            )

            # Use unified approach: send text content for extraction along with files
            text_for_extraction = content if intention == ConversationMessageIntention.EXTRACTION else None
            document_responses = await self.document_service.create_combined_message(document_data, text_for_extraction)

            if document_responses:
                response.files = document_responses

        if settings.append_collected_data_to_message_response:
            response.collected_data = aggregated_data.model_dump()

        return response

    async def get_suggested_prompts(
        self,
        conversation_id: UUID,
        user_message: UserMessageSerializer,
        files: list[UploadFile] | None,
        token: str,
        aggregated_data: AggregatedData,
        confirmed_data: ConfirmedData,
        intention: ConversationMessageIntention,
        conversation_message_history: list[CombinedMessageSerializer],
        current_reply_type: SystemReplyType | None = None,
    ) -> list[SuggestedUserPrompt]:
        dash_task_activity_id: int | None = await self.conversation_repository.get_dash_task_activity_id(
            conversation_id
        )

        # Generate suggested replies for the user
        suggested_prompts_generator = SuggestedPromptsGenerator(
            conversation_id=conversation_id,
            user_message=user_message,
            conversation_message_history=conversation_message_history,
            intention=intention,
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data,
            files=files,
            dash_task_activity_id=dash_task_activity_id,
            current_reply_type=current_reply_type,
        )
        return await suggested_prompts_generator.run()

    async def create_message(self, message_data: MessageValidator) -> BaseMessageSerializer:
        """
        Create a new conversation message.

        Args:
            message_data: Data for creating the message

        Returns:
            Response with the created message data

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error creating the message
        """
        logger.debug('Creating new message for conversation ID: %s', message_data.conversation_id)
        try:
            return await self.conversation_message_repository.create(message_data)

        except Exception as e:  # pragma: no cover
            logger.error('Error creating message: %s', e)
            raise

    async def get(self, public_id: UUID) -> BaseMessageSerializer:
        """
        Get a message by its ID.

        Args:
            public_id: The ID of the message

        Returns:
            The message response

        Raises:
            EntityNotFoundError: If the message doesn't exist
            DatabaseException: If there's an error retrieving the message
        """
        try:
            logger.debug('Retrieving message with ID: %s', public_id)
            return await self.conversation_message_repository.get(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving message: %s', e)
            raise

    async def list(self, public_id: UUID) -> Sequence[BaseMessageSerializer]:
        """
        Get all messages for a specific conversation.

        Args:
            public_id: The ID of the conversation

        Returns:
            Sequence of messages for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the messages
        """
        if not await self.conversation_repository.exists(public_id):
            raise EntityNotFoundError('Conversation', str(public_id))

        try:
            logger.debug('Retrieving all messages for conversation with ID: %s', public_id)
            return await self.conversation_message_repository.list(public_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving messages for conversation %s: %s', public_id, e)
            raise

    async def get_last(self, public_id: UUID, token: str) -> BaseMessageSerializer:
        """
        Get the last message for a specific conversation.

        Args:
            public_id: The ID of the conversation
            token: The authentication token

        Returns:
            The last message for the conversation

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
            DatabaseException: If there's an error retrieving the last message
        """
        try:
            logger.debug('Retrieving last message for conversation with ID: %s', public_id)

            last_message = await self.conversation_message_repository.get_last(public_id)
            # Check if the last message is from a user and we should generate a system response

            if last_message.role != MessageRole.USER:
                return last_message

            logger.debug('Last message is from user, generating system response for conversation %s', public_id)

            try:
                conversation_message_history = await self.conversation_message_repository.get_combined_history(
                    public_id
                )

                # Get aggregated data for the conversation
                aggregated_data = await self.extracted_data_service.aggregate_data(public_id)

                # Handle confirmed data logic
                await self._handle_confirmed_data_update(public_id, aggregated_data, token)

                confirmed_data = await self.conversation_repository.get_confirmed_data(public_id)
                conversation = await self.conversation_repository.get(public_id)
                if not conversation:
                    raise EntityNotFoundError('Conversation', str(public_id))

                # Check if we should use client name single confirmation message
                should_use_client_confirmation = (
                    conversation
                    and str(conversation.State) == str(ConversationState.COLLECTING_CLIENT_NAME)
                    and aggregated_data.is_complete
                    and not confirmed_data.client_name
                )

                if should_use_client_confirmation:
                    reply_type = SystemReplyType.CLIENT_NAME_SINGLE_CONFIRMATION
                    # Get filename from the previous user message
                    filenames = await self.document_db_repository.get_filenames_for_message(last_message.id)
                    filename = filenames[0] if filenames else None  # no files uploaded

                    if filename:
                        filename = filename.split('.')[0]
                        formatted_content = ALL_REQUIRED_FIELDS_EXTRACTED_DOCS.format(
                            client_name=aggregated_data.client_name[0], filename=filename
                        )
                    else:
                        formatted_content = reply_type.message_text.format(client_name=aggregated_data.client_name[0])

                    extracted_options = []

                else:
                    # Get confirmed data to avoid showing options for already confirmed fields
                    confirmed_data = await self.conversation_repository.get_confirmed_data(public_id)

                    # Format the extracted data message
                    formatted_content, reply_type = self.system_message_service.generate_system_message(
                        aggregated_data, confirmed_data
                    )
                    extracted_options = self.system_message_service.generate_options(
                        aggregated_data, confirmed_data, ConversationState(conversation.State)
                    )

                suggested_prompts = await self.get_suggested_prompts(
                    conversation_id=public_id,
                    user_message=cast(UserMessageSerializer, last_message),
                    files=None,
                    token=token,
                    aggregated_data=aggregated_data,
                    confirmed_data=confirmed_data,
                    intention=ConversationMessageIntention.EXTRACTION,
                    conversation_message_history=conversation_message_history,
                    current_reply_type=reply_type,
                )
                # Create system message
                system_message_data = MessageValidator(
                    conversation_id=public_id,
                    role=MessageRole.SYSTEM,
                    type=last_message.type,
                    content=formatted_content,
                    system_reply_type=reply_type,
                    options=extracted_options,
                    suggested_prompts=[str(i) for i in suggested_prompts],
                )

                # Create and return the system message
                system_message = await self.create_message(system_message_data)
                logger.info('Generated system message %s for conversation %s', system_message.id, public_id)
                return system_message

            except Exception as e:
                logger.warning('Failed to generate system message for conversation %s: %s', public_id, e)
                raise

        except Exception as e:  # pragma: no cover
            logger.error('Error retrieving last message for conversation %s: %s', public_id, e)
            raise

    async def delete_many(self, conversation_id: UUID) -> None:
        """
        Delete all messages for a specific conversation.

        Args:
            conversation_id: The ID of the conversation

        Returns:
            None

        Raises:
            DatabaseException: If there's an error deleting the messages
        """
        try:
            logger.debug('Deleting all messages for conversation with ID: %s', conversation_id)
            await self.conversation_message_repository.delete_many(conversation_id)

        except Exception as e:  # pragma: no cover
            logger.error('Error deleting messages for conversation %s: %s', conversation_id, e)
            raise

    async def update_message_fields(self, message_id: UUID, fields_to_update: dict) -> None:
        """
        Update specific fields of a conversation message.

        Args:
            message_id: The ID of the message to update.
            fields_to_update: A dictionary where keys are field names and values are the new values.
        """
        try:
            logger.debug('Updating message %s with fields: %s', message_id, fields_to_update)
            await self.conversation_message_repository.update_fields(message_id, fields_to_update)
        except Exception as e:
            logger.error('Error updating message %s: %s', message_id, e)
            raise

    def _convert_to_option_objects(
        self, raw_options: List | None, conversation_state: ConversationState
    ) -> List[Option]:
        """Convert raw options to proper option objects based on conversation state."""
        if not raw_options or not conversation_state:
            return []

        if conversation_state == ConversationState.COLLECTING_CLIENT_NAME:
            return [ClientNameOption(client_name=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_COUNTRY:
            return [LDMFCountryOption(ldmf_country=name) for name in raw_options]
        elif conversation_state == ConversationState.COLLECTING_DATES:
            return [DatePickerOption(start_date=start_date, end_date=end_date) for start_date, end_date in raw_options]
        return []

    async def _get_system_message_with_result(
        self,
        message_data: MessageValidator,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> MessageValidator:
        """
        Create a system message based on the message processing result.
        """
        options = []
        # Check if we have options from the processing result
        if 'options' in processing_result.data and processing_result.data['options']:
            # Check conversation state to determine option type
            conversation_state = processing_result.data.get('conversation_state')
            if conversation_state:
                options = self._convert_to_option_objects(processing_result.data['options'], conversation_state)

        return MessageValidator(
            conversation_id=message_data.conversation_id,
            role=MessageRole.SYSTEM,
            type=message_data.type,
            content=processing_result.system_reply,
            system_reply_type=processing_result.system_reply_type,
            options=options,
            suggested_prompts=[i.value for i in suggested_prompts],
        )

    async def get_owner_id(self, message_id: UUID) -> UUID | None:
        """
        Get an owner ID for the message.

        Args:
            message_id: The ID of the message

        Returns:
            The user ID if the message exists, None otherwise
        """
        try:
            logger.debug('Retrieving an owner ID for the message: %s', message_id)
            return await self.conversation_message_repository.get_owner_id(message_id)

        except Exception:  # pragma: no cover
            logger.exception('Failed to retrieve message owner ID')
            raise

    async def _handle_country_selection(
        self, selected_option: LDMFCountryOption, conversation_id: UUID
    ) -> MessageValidator:
        try:
            logger.debug(f'Handling selection {selected_option.ldmf_country} for conversation {conversation_id}')

            # Update confirmed data with the selected country
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='ldmf_country',
                field_value=selected_option.ldmf_country,
                state=ConversationState.COLLECTING_DATES,
            )

            # Create confirmation message
            reply_type = SystemReplyType.LDMF_COUNTRY_CONFIRMED
            confirmation_message = reply_type.message_text.format(ldmf_country=selected_option.ldmf_country)
            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling country selection: %s', e)
            raise

    async def _handle_dates_selection(
        self, selected_option: DatePickerOption, conversation_id: UUID
    ) -> MessageValidator:
        """
        Handle dates selection from options.

        Args:
            selected_option: The selected dates option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug('Handling dates selection: %s for conversation: %s', selected_option, conversation_id)
            start_date = selected_option.start_date.isoformat() if selected_option.start_date else None
            end_date = selected_option.end_date.isoformat() if selected_option.end_date else None

            # Create confirmation message
            reply_type = SystemReplyType.DATES_CONFIRMED
            confirmation_message = reply_type.message_text
            options = []

            if start_date and end_date:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_OBJECTIVE,  # Move to next state
                )
            else:
                await self.extracted_data_service.update_confirmed_data(
                    conversation_id=conversation_id,
                    field_name=ConfirmedDataFields.DATE_INTERVALS.value,
                    field_value=(start_date, end_date),
                    state=ConversationState.COLLECTING_DATES,  # Stay in the same state
                )

                reply_type = SystemReplyType.DATES_ONE_DATE
                confirmation_message = reply_type.message_text
                options = [selected_option]

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                options=options,
                system_reply_type=reply_type,
            )

        except Exception as e:
            logger.error('Error handling dates selection: %s', e)
            raise

    async def _handle_client_name_selection(
        self, selected_option: ClientNameOption, conversation_id: UUID
    ) -> MessageValidator:
        """
        Handle client name selection from options.

        Args:
            selected_option: The selected client name option
            conversation_id: The conversation ID

        Returns:
            MessageValidator: System message confirming the selection

        Raises:
            EntityNotFoundError: If the conversation doesn't exist
        """
        try:
            logger.debug(
                'Handling client name selection: %s for conversation: %s', selected_option.client_name, conversation_id
            )

            # Update confirmed data with the selected client name
            await self.extracted_data_service.update_confirmed_data(
                conversation_id=conversation_id,
                field_name='client_name',
                field_value=selected_option.client_name,
                state=ConversationState.COLLECTING_COUNTRY,  # Move to next state
            )

            # Create confirmation message
            reply_type = SystemReplyType.CLIENT_NAME_CONFIRMED
            confirmation_message = reply_type.message_text.format(client_name=selected_option.client_name)

            return MessageValidator(
                conversation_id=conversation_id,
                role=MessageRole.SYSTEM,
                type=MessageType.TEXT,
                content=confirmation_message,
                selected_option=selected_option,
                system_reply_type=reply_type,
            )

        except Exception as e:  # pragma: no cover
            logger.error('Error handling client name selection: %s', e)
            raise

    async def _handle_confirmed_data_update(self, public_id: UUID, aggregated_data: AggregatedData, token: str) -> None:
        """
        Handle confirmed data logic based on aggregated data state.

        Args:
            public_id: The conversation ID
            aggregated_data: The aggregated data from all sources
        """
        try:
            # Fetch confirmed data from existing conversation
            confirmed_data = await self.conversation_repository.get_confirmed_data(public_id)

            # Check if we need to update confirmed data
            should_update = False

            # If confirmed data is empty and aggregated has all required fields with single values
            if confirmed_data.is_empty and aggregated_data.is_complete:
                logger.info('Confirmed data is empty and aggregated data is complete for conversation %s', public_id)
                # Apply all fields from aggregated except client_name
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            elif confirmed_data.is_empty and not aggregated_data.is_complete:
                logger.info(
                    'Confirmed data is empty and aggregated data is not complete for conversation %s', public_id
                )
                should_update, confirmed_data = await self._update_fields_from_aggregated_data(
                    aggregated_data, confirmed_data, token
                )

            # If confirmed data has some fields - update from aggregated
            elif not confirmed_data.is_empty and not confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data has some fields, updating from aggregated for conversation %s', public_id)

                # Update ldmf_country if not confirmed and available in aggregated
                if (
                    not confirmed_data.ldmf_country
                    and aggregated_data.ldmf_country
                    and len(aggregated_data.ldmf_country) == 1
                ):
                    verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                        aggregated_data.ldmf_country[0], token
                    )
                    if verified_countries and len(verified_countries) == 1:
                        confirmed_data.ldmf_country = verified_countries[0]
                        should_update = True

                # Update date_intervals if not confirmed and available in aggregated
                if (
                    not confirmed_data.date_intervals
                    and aggregated_data.date_intervals
                    and len(aggregated_data.date_intervals) == 1
                ):
                    start_date, end_date = aggregated_data.date_intervals[0]
                    if self._is_date_unambiguous_and_complete(start_date, end_date):
                        confirmed_data.date_intervals = (start_date, end_date)
                        should_update = True

                # Update objective_and_scope if not confirmed and available in aggregated
                if not confirmed_data.objective_and_scope and aggregated_data.objective_and_scope:
                    confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
                    should_update = True

                # Update outcomes if not confirmed and available in aggregated
                if not confirmed_data.outcomes and aggregated_data.outcomes:
                    confirmed_data.outcomes = aggregated_data.outcomes
                    should_update = True

            # If confirmed data has all fields - do nothing
            elif confirmed_data.required_fields_are_complete:
                logger.info('Confirmed data is complete, no update needed for conversation %s', public_id)
                return

            # Save to database and set state to collecting client name if confirmed data was updated
            if should_update:
                logger.info(
                    'Updating confirmed data and setting state to COLLECTING_CLIENT_NAME for conversation %s', public_id
                )
                await self.conversation_repository.update_confirmed_data_and_state(
                    public_id=public_id,
                    confirmed_data=confirmed_data,
                    state=ConversationState.COLLECTING_CLIENT_NAME,
                )

        except Exception:  # pragma: no cover
            logger.exception('Error handling confirmed data update for conversation %s', public_id)
            raise

    async def _update_fields_from_aggregated_data(
        self, aggregated_data: AggregatedData, confirmed_data: ConfirmedData, token: str
    ) -> tuple[bool, ConfirmedData]:
        should_update = False
        if aggregated_data.ldmf_country and len(aggregated_data.ldmf_country) == 1:
            verified_countries = await self.extracted_data_service.ldmf_country_service.verify_ldmf_country(
                aggregated_data.ldmf_country[0], token
            )
            if verified_countries and len(verified_countries) == 1:
                confirmed_data.ldmf_country = verified_countries[0]
                should_update = True

        if aggregated_data.date_intervals and len(aggregated_data.date_intervals) == 1:
            start_date, end_date = aggregated_data.date_intervals[0]
            if self._is_date_unambiguous_and_complete(start_date, end_date):
                confirmed_data.date_intervals = (start_date, end_date)
                should_update = True

        if aggregated_data.objective_and_scope:
            confirmed_data.objective_and_scope = aggregated_data.objective_and_scope
            should_update = True

        if aggregated_data.outcomes:
            confirmed_data.outcomes = aggregated_data.outcomes
            should_update = True

        return should_update, confirmed_data

    @staticmethod
    def _is_date_unambiguous_and_complete(start_date: str | None, end_date: str | None) -> bool:
        """Check if the date is unambiguous and complete."""
        if not start_date or not end_date:
            return False

        processed_start_date = date.fromisoformat(start_date)
        processed_end_date = date.fromisoformat(end_date)

        return processed_start_date.day > 12 and processed_end_date.day > 12

    async def _get_dash_discard_response(
        self,
        user_message: UserMessageSerializer,
        processing_result: ConversationMessageProcessingResult,
        suggested_prompts: Sequence[SuggestedUserPrompt],
    ) -> CombinedMessageSerializer:
        message = MessageValidator(
            conversation_id=user_message.conversation_id,
            role=MessageRole.SYSTEM,
            type=user_message.type,
            content=processing_result.system_reply,
            suggested_prompts=[i.value for i in suggested_prompts],
            system_reply_type=processing_result.system_reply_type,
        )

        response = CombinedMessageSerializer(
            user=user_message,
            system=cast(SystemMessageSerializer, await self.create_message(message)),
        )

        return response
