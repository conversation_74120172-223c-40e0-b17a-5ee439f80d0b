from dataclasses import dataclass
import logging

from constants.extracted_data import FieldCompletionStatus, RequiredField
from constants.message import SystemReplyType
from schemas import AggregatedData, ConfirmedData, FieldStatusInfo


__all__ = ['SystemMessagePromptsGenerator']

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class SystemMessagePromptsGenerator:
    """Service for generating suggested prompts for system messages based on aggregated and confirmed data."""

    _FIELD_COLLECTION_ORDER = [
        RequiredField.CLIENT_INFO,
        RequiredField.LDMF_COUNTRY,
        RequiredField.ENGAGEMENT_DATES,
        RequiredField.OBJECTIVE_SCOPE,
        RequiredField.OUTCOMES,
    ]

    _REQUIRED_FIELDS = {
        RequiredField.CLIENT_INFO: 'Client Name',
        RequiredField.LDMF_COUNTRY: 'Lead Deloitte Member Firm Country',
        RequiredField.ENGAGEMENT_DATES: 'Engagement Dates',
        RequiredField.OBJECTIVE_SCOPE: 'Objective and Scope',
        RequiredField.OUTCOMES: 'Outcomes',
    }

    aggregated_data: AggregatedData
    confirmed_data: ConfirmedData

    def generate_system_reply_type(self) -> SystemReplyType | None:
        """
        Generate suggested prompts based on the next required field and available data.

        Returns:
            List of suggested prompt strings for the user
        """
        field_status = self._get_field_status()
        next_field = self._get_next_required_field(field_status)

        if next_field == RequiredField.LDMF_COUNTRY and len(field_status[RequiredField.LDMF_COUNTRY].value) > 1:  # type: ignore
            return SystemReplyType.LDMF_COUNTRY_MULTIPLE_OPTIONS

        return None

    def _get_field_status(self) -> dict[RequiredField, FieldStatusInfo]:
        """Get the status of all required fields."""

        return {
            RequiredField.CLIENT_INFO: self._check_field_status(
                confirmed_value=self.confirmed_data.client_name,
                aggregated_value=self.aggregated_data.client_name,
                display_name=self._REQUIRED_FIELDS[RequiredField.CLIENT_INFO],
            ),
            RequiredField.LDMF_COUNTRY: self._check_field_status(
                confirmed_value=self.confirmed_data.ldmf_country,
                aggregated_value=self.aggregated_data.ldmf_country,
                display_name=self._REQUIRED_FIELDS[RequiredField.LDMF_COUNTRY],
            ),
            RequiredField.ENGAGEMENT_DATES: self._check_field_status(
                confirmed_value=self.confirmed_data.date_intervals,
                aggregated_value=self.aggregated_data.date_intervals,
                display_name=self._REQUIRED_FIELDS[RequiredField.ENGAGEMENT_DATES],
            ),
            RequiredField.OBJECTIVE_SCOPE: self._check_field_status(
                confirmed_value=self.confirmed_data.objective_and_scope,
                aggregated_value=self.aggregated_data.objective_and_scope,
                display_name=self._REQUIRED_FIELDS[RequiredField.OBJECTIVE_SCOPE],
            ),
            RequiredField.OUTCOMES: self._check_field_status(
                confirmed_value=self.confirmed_data.outcomes,
                aggregated_value=self.aggregated_data.outcomes,
                display_name=self._REQUIRED_FIELDS[RequiredField.OUTCOMES],
            ),
        }

    def _check_field_status(
        self, confirmed_value: str | list | tuple | None, aggregated_value: str | list | None, display_name: str
    ) -> 'FieldStatusInfo':
        """Helper method to check field status based on confirmed and aggregated values."""

        def normalize_value(value):
            if isinstance(value, tuple):
                return list(value)
            return value

        if confirmed_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.COMPLETED,
                value=normalize_value(confirmed_value),
                display_name=display_name,
            )
        elif aggregated_value:
            return FieldStatusInfo(
                status=FieldCompletionStatus.PENDING_CONFIRMATION,
                value=normalize_value(aggregated_value),
                display_name=display_name,
            )
        else:
            return FieldStatusInfo(status=FieldCompletionStatus.MISSING, value=None, display_name=display_name)

    def _get_next_required_field(self, field_status: dict[RequiredField, FieldStatusInfo]) -> RequiredField | None:
        """Get the next required field that needs attention."""
        for field in self._FIELD_COLLECTION_ORDER:
            field_info = field_status[field]
            if field_info.status not in [FieldCompletionStatus.COMPLETED]:
                return field
        return None
