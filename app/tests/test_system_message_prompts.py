import pytest

from constants.extracted_data import <PERSON><PERSON>om<PERSON>tion<PERSON>tatus, Required<PERSON><PERSON>
from schemas.extracted_data import AggregatedData
from schemas.confirmed_data import ConfirmedData
from services.system_message_prompts import SystemMessagePromptsGenerator


class TestSystemMessagePromptsGenerator:
    """Test cases for SystemMessagePromptsGenerator service."""

    def test_generate_prompts_for_empty_data(self):
        """Test prompt generation when no data is available."""
        aggregated_data = AggregatedData()
        confirmed_data = ConfirmedData()
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return prompts for the first missing field (client info)
        assert len(prompts) == 3
        assert "Tell me about your client" in prompts
        assert "What is the client name?" in prompts
        assert "Provide client information" in prompts

    def test_generate_prompts_for_client_confirmation(self):
        """Test prompt generation when client data needs confirmation."""
        aggregated_data = AggregatedData(client_name=["Test Client"])
        confirmed_data = ConfirmedData()
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return confirmation prompts for client name
        assert len(prompts) == 6  # 3 base + 3 context-specific
        assert "Confirm the client name" in prompts
        assert "Is this the correct client?" in prompts
        assert "Yes, that's correct" in prompts

    def test_generate_prompts_for_multiple_client_options(self):
        """Test prompt generation when multiple client options are available."""
        aggregated_data = AggregatedData(client_name=["Client A", "Client B"])
        confirmed_data = ConfirmedData()
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return confirmation prompts with selection options
        assert "Select the correct option" in prompts
        assert "Choose from the available options" in prompts
        assert "Pick the right one" in prompts

    def test_generate_prompts_for_ldmf_country_missing(self):
        """Test prompt generation when client is confirmed but LDMF country is missing."""
        aggregated_data = AggregatedData(client_name=["Test Client"])
        confirmed_data = ConfirmedData(client_name="Test Client")
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return prompts for LDMF country (next field)
        assert "Which country is the Lead Deloitte Member Firm in?" in prompts
        assert "Specify the LDMF country" in prompts
        assert "What is the Lead Deloitte Member Firm country?" in prompts

    def test_generate_prompts_for_engagement_dates_missing(self):
        """Test prompt generation when client and LDMF are confirmed but dates are missing."""
        aggregated_data = AggregatedData(
            client_name=["Test Client"],
            ldmf_country=["United States"]
        )
        confirmed_data = ConfirmedData(
            client_name="Test Client",
            ldmf_country="United States"
        )
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return prompts for engagement dates
        assert "When did the engagement take place?" in prompts
        assert "Provide engagement dates" in prompts
        assert "What are the start and end dates?" in prompts

    def test_generate_prompts_for_all_fields_complete(self):
        """Test prompt generation when all required fields are complete."""
        aggregated_data = AggregatedData(
            client_name=["Test Client"],
            ldmf_country=["United States"],
            date_intervals=[("2023-01-01", "2023-12-31")],
            objective_and_scope="Test objective",
            outcomes="Test outcomes"
        )
        confirmed_data = ConfirmedData(
            client_name="Test Client",
            ldmf_country="United States",
            date_intervals=("2023-01-01", "2023-12-31"),
            objective_and_scope="Test objective",
            outcomes="Test outcomes"
        )
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        prompts = generator.generate_system_reply_type()
        
        # Should return completion prompts
        assert "Create my qual" in prompts
        assert "Generate the qualification" in prompts
        assert "I'm ready to proceed" in prompts

    def test_field_status_detection(self):
        """Test internal field status detection logic."""
        aggregated_data = AggregatedData(
            client_name=["Test Client"],
            ldmf_country=["US", "UK"]  # Multiple options
        )
        confirmed_data = ConfirmedData(client_name="Test Client")
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        field_status = generator._get_field_status()
        
        # Client should be completed
        assert field_status[RequiredField.CLIENT_INFO].status == FieldCompletionStatus.COMPLETED
        
        # LDMF should be pending confirmation
        assert field_status[RequiredField.LDMF_COUNTRY].status == FieldCompletionStatus.PENDING_CONFIRMATION
        
        # Dates should be missing
        assert field_status[RequiredField.ENGAGEMENT_DATES].status == FieldCompletionStatus.MISSING

    def test_next_required_field_detection(self):
        """Test detection of the next required field."""
        aggregated_data = AggregatedData(client_name=["Test Client"])
        confirmed_data = ConfirmedData(client_name="Test Client")
        
        generator = SystemMessagePromptsGenerator(
            aggregated_data=aggregated_data,
            confirmed_data=confirmed_data
        )
        
        field_status = generator._get_field_status()
        next_field = generator._get_next_required_field(field_status)
        
        # Should be LDMF country since client is complete
        assert next_field == RequiredField.LDMF_COUNTRY
